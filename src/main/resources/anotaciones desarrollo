- trabajar en como se guardan los modelos para no chancar las relaciones ni saltarnos los constraints en VehiculoService

- Hacer una interfaz con las opciones del Sistema por ejemplouna que necesitaremos es eliminar
los SaleGui con su respectivo SearchProductGui y su Sale para los Sale con private Boolean esProforma=true;
entonces necesitamos setear cada cuanto tiempo hacerlo, por ejemplo cada 1 semana.

"""
Mejora semántica del tipo de retorno
Actualmente usas Mono<Boolean> en SaleService, que es totalmente válido.
Pero si en el futuro quieres manejar mejor errores específicos, podrías usar:

public Mono<Either<ErrorTipo, SaleResultado>> iniciarVentaContado(...)
O incluso:
public Mono<ResultStatus>

Esto hace que el cliente sepa por qué falló:
Sin stock, Error de conexión, Usuario no autenticado, etc.

Pero si por ahora solo necesitas saber si salió bien o mal, tu implementación actual es clara y directa. ✅
"""