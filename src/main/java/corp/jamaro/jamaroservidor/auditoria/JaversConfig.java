package corp.jamaro.jamaroservidor.auditoria;

import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.repository.sql.ConnectionProvider;
import org.javers.repository.sql.DialectName;
import org.javers.repository.sql.SqlRepositoryBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.sql.DriverManager;
import java.sql.SQLException;

@Configuration
public class JaversConfig {

    @Value("${jamaro.javers.url}")
    private String javersUrl;

    @Value("${jamaro.javers.username}")
    private String javersUser;

    @Value("${jamaro.javers.password}")
    private String javersPass;

    @Value("${jamaro.javers.schema:public}") // valor por defecto 'public'
    private String javersSchema;

    @Value("${jamaro.javers.dialect:POSTGRES}")
    private String javersDialect; // lo convertiremos luego a DialectName

    @Bean
    public ConnectionProvider javersConnectionProvider() {
        return () -> {
            try {
                return DriverManager.getConnection(javersUrl, javersUser, javersPass);
            } catch (SQLException e) {
                throw new RuntimeException("Error creando conexión a Javers DB", e);
            }
        };
    }

    @Bean
    public Javers javers(ConnectionProvider connectionProvider) {
        DialectName dialectName = DialectName.valueOf(javersDialect.toUpperCase());
        return JaversBuilder.javers()
                .registerJaversRepository(
                        SqlRepositoryBuilder
                                .sqlRepository()
                                .withConnectionProvider(connectionProvider)
                                .withSchema(javersSchema)
                                .withDialect(dialectName)
                                .build()
                )
                .build();
    }
}
