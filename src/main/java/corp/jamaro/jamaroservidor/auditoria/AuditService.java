package corp.jamaro.jamaroservidor.auditoria;

import lombok.RequiredArgsConstructor;
import org.javers.core.Javers;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * Servicio de auditoría que centraliza las operaciones con JaVers,
 * de manera reactiva.
 */
@Service
@RequiredArgsConstructor
public class AuditService {

    private final Javers javers;

    /**
     * Realiza un commit en JaVers de la entidad dada, con el autor especificado,
     * ejecutándolo en un boundedElastic (ya que JaVers es bloqueante internamente).
     *
     * @param entity Objeto a auditar.
     * @param author Nombre o identificador del autor de la modificación.
     * @return Mono<Void> que completará cuando se haya hecho el commit.
     */
    public Mono<Void> commit(Object entity, String author) {
        return Mono.fromRunnable(() -> javers.commit(author, entity))
                .subscribeOn(Schedulers.boundedElastic()).then();
    }
}
