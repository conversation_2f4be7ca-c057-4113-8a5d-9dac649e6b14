package corp.jamaro.jamaroservidor.app.controller.task;

import corp.jamaro.jamaroservidor.app.model.file.BucketFile;
import corp.jamaro.jamaroservidor.app.model.task.UploadFileTask;
import corp.jamaro.jamaroservidor.app.service.task.UploadFileTaskService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Controlador RSocket para operaciones específicas de UploadFileTask.
 * Se exponen métodos para CRUD y acciones puntuales como setProgress, markAsCompleted, etc.
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class UploadFileTaskController {

    private final UploadFileTaskService uploadFileTaskService;

    // =========================================================================
    //                            CREATE / UPDATE
    // =========================================================================

    /**
     * Crea o actualiza una UploadFileTask.
     * El cliente enviará un objeto completo de UploadFileTask.
     */
    @MessageMapping("uploadFileTask.save")
    public Mono<UploadFileTask> saveUploadFileTask(UploadFileTask uploadFileTask) {
        log.debug("RSocket - uploadFileTask.save: title='{}'", uploadFileTask.getTitle());
        return uploadFileTaskService.createOrUpdate(uploadFileTask);
    }

    // =========================================================================
    //                            READ / GET
    // =========================================================================

    /**
     * Retorna todas las UploadFileTask existentes.
     */
    @MessageMapping("uploadFileTask.getAll")
    public Flux<UploadFileTask> getAllUploadFileTasks() {
        log.debug("RSocket - uploadFileTask.getAll");
        return uploadFileTaskService.getAll();
    }

    /**
     * Obtiene una UploadFileTask por su ID.
     */
    @MessageMapping("uploadFileTask.getById")
    public Mono<UploadFileTask> getUploadFileTaskById(UUID id) {
        log.debug("RSocket - uploadFileTask.getById: id={}", id);
        return uploadFileTaskService.getById(id);
    }

    // =========================================================================
    //                            DELETE
    // =========================================================================

    /**
     * Elimina la UploadFileTask identificada por ID.
     */
    @MessageMapping("uploadFileTask.delete")
    public Mono<Void> deleteUploadFileTask(UUID id) {
        log.debug("RSocket - uploadFileTask.delete: id={}", id);
        return uploadFileTaskService.deleteById(id);
    }

    // =========================================================================
    //                            ACCIONES / ESTADOS
    // =========================================================================

    /**
     * Marca la tarea como completada, poniendo progress en 100%.
     */
    @MessageMapping("uploadFileTask.markAsCompleted")
    public Mono<UploadFileTask> markAsCompleted(UUID id) {
        log.debug("RSocket - uploadFileTask.markAsCompleted: id={}", id);
        return uploadFileTaskService.markAsCompleted(id);
    }

    /**
     * Ajusta el progreso de la tarea. Si es menor a 100, cambia estado a EN_PROGRESO.
     */
    @MessageMapping("uploadFileTask.setProgress")
    public Mono<UploadFileTask> setProgress(SetProgressRequest request) {
        log.debug("RSocket - uploadFileTask.setProgress: id={}, newProgress={}",
                request.getTaskId(), request.getNewProgress());
        return uploadFileTaskService.setProgress(request.getTaskId(), request.getNewProgress());
    }

    // =========================================================================
    //                    ACTUALIZAR / REEMPLAZAR BucketFile
    // =========================================================================

    /**
     * Actualiza el BucketFile relacionado con esta tarea.
     */
    @MessageMapping("uploadFileTask.updateBucketFile")
    public Mono<UploadFileTask> updateBucketFile(UpdateBucketFileRequest request) {
        log.debug("RSocket - uploadFileTask.updateBucketFile: taskId={}, newObjectName='{}'",
                request.getTaskId(),
                request.getBucketFile() != null ? request.getBucketFile().getObjectName() : "null");
        return uploadFileTaskService.updateBucketFile(request.getTaskId(), request.getBucketFile());
    }

    // =========================================================================
    //                          CLASES DE REQUEST
    // =========================================================================

    /**
     * Clase de request para el setProgress,
     * encapsulando id de la tarea y el nuevo valor de progreso.
     */
    @Data
    public static class SetProgressRequest {
        private UUID taskId;
        private float newProgress;
    }

    /**
     * Clase de request para actualizar el BucketFile de la tarea,
     * encapsulando id de la tarea y el BucketFile nuevo.
     */
    @Data
    public static class UpdateBucketFileRequest {
        private UUID taskId;
        private BucketFile bucketFile;
    }
}
