package corp.jamaro.jamaroservidor.app.controller;


import corp.jamaro.jamaroservidor.app.model.User;
import corp.jamaro.jamaroservidor.app.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;

@Controller
@RequiredArgsConstructor
public class UserRSocketController {
    private final UserRepository userRepository;

    @PreAuthorize("hasRole('ADMIN')") // ejemplo: solo ADMIN puede listar
    @MessageMapping("users.all")
    public Flux<User> getAllUsers() {
        return userRepository.findAll();
    }
}
