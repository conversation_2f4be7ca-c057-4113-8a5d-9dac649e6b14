package corp.jamaro.jamaroservidor.app.repository;

import corp.jamaro.jamaroservidor.app.model.User;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface UserRepository extends ReactiveNeo4jRepository<User, UUID> {
    Mono<User> findByUsername(String username);
    Mono<User> findByDocumento(String documento);
    Mono<Void> deleteByUsername(String username);
}
