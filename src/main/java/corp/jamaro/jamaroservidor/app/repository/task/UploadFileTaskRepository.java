package corp.jamaro.jamaroservidor.app.repository.task;

import corp.jamaro.jamaroservidor.app.model.task.UploadFileTask;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.UUID;

@Repository
public interface UploadFileTaskRepository extends ReactiveNeo4jRepository<UploadFileTask, UUID> {

}
