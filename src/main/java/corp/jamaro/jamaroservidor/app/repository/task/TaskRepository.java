package corp.jamaro.jamaroservidor.app.repository.task;

import corp.jamaro.jamaroservidor.app.model.task.Task;
import org.springframework.data.neo4j.repository.ReactiveNeo4jRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface TaskRepository extends ReactiveNeo4jRepository<Task, UUID> {

    // Ejemplo de consulta personalizada:
    // @Query("MATCH (t:Task)-[:ASIGNADO_AL_USUARIO]->(u:User) WHERE u.id = $userId RETURN t")
    // Flux<Task> findByAssignedUser(UUID userId);

}
