package corp.jamaro.jamaroservidor.app.vehiculo.controller;

import corp.jamaro.jamaroservidor.app.vehiculo.model.*;
import corp.jamaro.jamaroservidor.app.vehiculo.service.VehiculoService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Controlador principal para la gestión de Vehiculos y borradores (VehiculoDraft),
 * así como de las entidades asociadas (Marcas, Modelos, Motores, etc.).
 *
 * Se exponen métodos vía RSocket con @MessageMapping para que el cliente
 * invoque cada funcionalidad de forma reactiva.
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class VehiculoController {

    private final VehiculoService vehiculoService;

    // =========================================================================
    //                           VEHICULO
    // =========================================================================

    @MessageMapping("vehiculo.exists")
    public Mono<Boolean> existsVehiculo(UUID vehiculoId) {
        log.debug("RSocket - vehiculo.exists: ID={}", vehiculoId);
        return vehiculoService.existsVehiculo(vehiculoId);
    }

    @MessageMapping("vehiculo.getAll")
    public Flux<Vehiculo> getAllVehiculos() {
        log.debug("RSocket - vehiculo.getAll: Obtener todos los Vehiculos");
        return vehiculoService.getAllVehiculos();
    }

    @MessageMapping("vehiculo.save")
    public Mono<Vehiculo> saveVehiculo(Vehiculo vehiculo) {
        log.debug("RSocket - vehiculo.save: Guardar/editar Vehiculo con ID={}", vehiculo.getId());
        return vehiculoService.saveVehiculo(vehiculo);
    }

    /**
     * Encuentra un Vehiculo por el ID de un VehiculoNombre relacionado.
     */
    @MessageMapping("vehiculo.getByVehiculoNombre")
    public Mono<Vehiculo> getVehiculoByVehiculoNombreId(UUID vehiculoNombreId) {
        log.debug("RSocket - vehiculo.getByVehiculoNombre: ID={}", vehiculoNombreId);
        return vehiculoService.getVehiculoByVehiculoNombreId(vehiculoNombreId);
    }

    @MessageMapping("vehiculo.delete")
    public Mono<Void> deleteVehiculo(UUID vehiculoId) {
        log.debug("RSocket - vehiculo.delete: ID={}", vehiculoId);
        return vehiculoService.deleteVehiculoById(vehiculoId);
    }

    @MessageMapping("vehiculoDraft.get")
    public Mono<Vehiculo> getDraftVehiculo() {
        log.debug("RSocket - vehiculoDraft.get (sin param): Obtener o crear draft para el usuario actual");
        return vehiculoService.getDraftVehiculo();
    }

    // =========================================================================
    //                         VEHICULO NOMBRE
    // =========================================================================

    @MessageMapping("vehiculoNombre.save")
    public Mono<VehiculoNombre> saveVehiculoNombre(VehiculoNombre nombre) {
        log.debug("RSocket - vehiculoNombre.save: nombre={}", nombre.getNombre());
        return vehiculoService.saveVehiculoNombre(nombre);
    }

    @MessageMapping("vehiculoNombre.getAll")
    public Flux<VehiculoNombre> getAllVehiculoNombres() {
        log.debug("RSocket - vehiculoNombre.getAll");
        return vehiculoService.getAllVehiculoNombres();
    }

    @MessageMapping("vehiculoNombre.getByVehiculo")
    public Flux<VehiculoNombre> getVehiculoNombresByVehiculoId(UUID vehiculoId) {
        log.debug("RSocket - vehiculoNombre.getByVehiculo: vehiculoId={}", vehiculoId);
        return vehiculoService.getVehiculoNombresByVehiculoId(vehiculoId);
    }

    /**
     * Busca coincidencias en los nombres de VehiculoNombre ignorando
     * mayúsculas/minúsculas. Máximo 30 resultados (según lógica interna).
     */
    @MessageMapping("vehiculoNombre.search")
    public Flux<VehiculoNombre> searchVehiculoNombre(String searchTerm) {
        log.debug("RSocket - vehiculoNombre.search: searchTerm='{}'", searchTerm);
        return vehiculoService.searchVehiculoNombre(searchTerm);
    }

    @MessageMapping("vehiculoNombre.delete")
    public Mono<Void> deleteVehiculoNombre(UUID nombreId) {
        log.debug("RSocket - vehiculoNombre.delete: nombreId={}", nombreId);
        return vehiculoService.deleteVehiculoNombreById(nombreId);
    }

    // =========================================================================
    //                         VEHICULO MARCA
    // =========================================================================

    @MessageMapping("vehiculoMarca.getAll")
    public Flux<VehiculoMarca> getAllVehiculoMarcas() {
        log.debug("RSocket - vehiculoMarca.getAll");
        return vehiculoService.getAllVehiculoMarcas();
    }

    @MessageMapping("vehiculoMarca.save")
    public Mono<VehiculoMarca> saveVehiculoMarca(VehiculoMarca marca) {
        log.debug("RSocket - vehiculoMarca.save: ID={}, marca='{}'",
                marca.getId(), marca.getMarca());
        return vehiculoService.saveVehiculoMarca(marca);
    }

    @MessageMapping("vehiculoMarca.delete")
    public Mono<Void> deleteVehiculoMarca(UUID marcaId) {
        log.debug("RSocket - vehiculoMarca.delete: marcaId={}", marcaId);
        return vehiculoService.deleteVehiculoMarcaById(marcaId);
    }

    // =========================================================================
    //                         VEHICULO MODELO
    // =========================================================================

    @MessageMapping("vehiculoModelo.save")
    public Mono<VehiculoModelo> saveVehiculoModelo(VehiculoModelo modelo) {
        log.debug("RSocket - vehiculoModelo.save: ID={}, modelo='{}'",
                modelo.getId(), modelo.getModelo());
        return vehiculoService.saveVehiculoModelo(modelo);
    }

    @MessageMapping("vehiculoModelo.delete")
    public Mono<Void> deleteVehiculoModelo(UUID modeloId) {
        log.debug("RSocket - vehiculoModelo.delete: modeloId={}", modeloId);
        return vehiculoService.deleteVehiculoModeloById(modeloId);
    }

    @MessageMapping("vehiculoModelo.getByMarca")
    public Flux<VehiculoModelo> getModelosByMarcaId(UUID marcaId) {
        log.debug("RSocket - vehiculoModelo.getByMarca: marcaId={}", marcaId);
        return vehiculoService.getModelosByMarcaId(marcaId);
    }

    @MessageMapping("vehiculoModelo.addToMarca")
    public Mono<VehiculoMarca> addVehiculoModeloToMarca(VehiculoModeloRequest request) {
        log.debug("RSocket - vehiculoModelo.addToMarca: marcaId={}, modelo='{}'",
                request.getMarcaId(),
                request.getVehiculoModelo() != null ? request.getVehiculoModelo().getModelo() : "null");
        return vehiculoService.addVehiculoModeloToMarca(
                request.getMarcaId(),
                request.getVehiculoModelo()
        );
    }

    // =========================================================================
    //                         VEHICULO MOTOR
    // =========================================================================

    @MessageMapping("vehiculoMotor.save")
    public Mono<VehiculoMotor> saveVehiculoMotor(VehiculoMotor motor) {
        log.debug("RSocket - vehiculoMotor.save: ID={}, motor='{}'",
                motor.getId(), motor.getMotor());
        return vehiculoService.saveVehiculoMotor(motor);
    }

    @MessageMapping("vehiculoMotor.delete")
    public Mono<Void> deleteVehiculoMotor(UUID motorId) {
        log.debug("RSocket - vehiculoMotor.delete: motorId={}", motorId);
        return vehiculoService.deleteVehiculoMotorById(motorId);
    }

    @MessageMapping("vehiculoMotor.getByMarca")
    public Flux<VehiculoMotor> getMotoresByMarcaId(UUID marcaId) {
        log.debug("RSocket - vehiculoMotor.getByMarca: marcaId={}", marcaId);
        return vehiculoService.getMotoresByMarcaId(marcaId);
    }

    @MessageMapping("vehiculoMotor.addToMarca")
    public Mono<VehiculoMarca> addVehiculoMotorToMarca(VehiculoMotorRequest request) {
        log.debug("RSocket - vehiculoMotor.addToMarca: marcaId={}, motor='{}'",
                request.getMarcaId(),
                request.getVehiculoMotor() != null ? request.getVehiculoMotor().getMotor() : "null");
        return vehiculoService.addVehiculoMotorToMarca(
                request.getMarcaId(),
                request.getVehiculoMotor()
        );
    }

    // =========================================================================
    //                         TIPO DE MOTOR
    // =========================================================================

    @MessageMapping("tipoDeMotor.getAll")
    public Flux<TipoDeMotor> getAllTipoDeMotor() {
        log.debug("RSocket - tipoDeMotor.getAll");
        return vehiculoService.getAllTipoDeMotor();
    }

    // =========================================================================
    //                         VEHICULO CILINDRADA
    // =========================================================================

    @MessageMapping("vehiculoCilindrada.getAll")
    public Flux<VehiculoCilindrada> getAllVehiculoCilindradas() {
        log.debug("RSocket - vehiculoCilindrada.getAll");
        return vehiculoService.getAllVehiculoCilindradas();
    }

    // =========================================================================
    //                         VEHICULO VERSION
    // =========================================================================

    @MessageMapping("vehiculoVersion.save")
    public Mono<VehiculoVersion> saveVehiculoVersion(VehiculoVersion version) {
        log.debug("RSocket - vehiculoVersion.save: ID={}, version='{}'",
                version.getId(), version.getVersion());
        return vehiculoService.saveVehiculoVersion(version);
    }

    @MessageMapping("vehiculoVersion.delete")
    public Mono<Void> deleteVehiculoVersion(UUID versionId) {
        log.debug("RSocket - vehiculoVersion.delete: versionId={}", versionId);
        return vehiculoService.deleteVehiculoVersionById(versionId);
    }

    @MessageMapping("vehiculoVersion.getByMarca")
    public Flux<VehiculoVersion> getVersionesByMarcaId(UUID marcaId) {
        log.debug("RSocket - vehiculoVersion.getByMarca: marcaId={}", marcaId);
        return vehiculoService.getVersionesByMarcaId(marcaId);
    }

    @MessageMapping("vehiculoVersion.addToMarca")
    public Mono<VehiculoMarca> addVehiculoVersionToMarca(VehiculoVersionRequest request) {
        log.debug("RSocket - vehiculoVersion.addToMarca: marcaId={}, version='{}'",
                request.getMarcaId(),
                request.getVehiculoVersion() != null ? request.getVehiculoVersion().getVersion() : "null");
        return vehiculoService.addVehiculoVersionToMarca(
                request.getMarcaId(),
                request.getVehiculoVersion()
        );
    }

    // =========================================================================
    //                         VEHICULO CARROCERIA
    // =========================================================================

    @MessageMapping("vehiculoCarroceria.getAll")
    public Flux<VehiculoCarroceria> getAllVehiculoCarrocerias() {
        log.debug("RSocket - vehiculoCarroceria.getAll");
        return vehiculoService.getAllVehiculoCarrocerias();
    }

    // =========================================================================
    //                         VEHICULO TRACCION
    // =========================================================================

    @MessageMapping("vehiculoTraccion.getAll")
    public Flux<VehiculoTraccion> getAllVehiculoTracciones() {
        log.debug("RSocket - vehiculoTraccion.getAll");
        return vehiculoService.getAllVehiculoTracciones();
    }

    // =========================================================================
    //                         VEHICULO TRANSMISION
    // =========================================================================

    @MessageMapping("vehiculoTransmision.getAll")
    public Flux<VehiculoTransmision> getAllVehiculoTransmisiones() {
        log.debug("RSocket - vehiculoTransmision.getAll");
        return vehiculoService.getAllVehiculoTransmisiones();
    }

    // =========================================================================
    //               Clases Request auxiliares para RSocket
    // =========================================================================

    /**
     * Para añadir imagen a un Vehiculo vía RSocket.
     * Incluye el flujo binario (fileData) y el 'orden' para la relación.
     */
    @Data
    public static class AddImageToVehRequest {
        private UUID vehiculoId;
        private Integer orden;
        // El flujo binario de la imagen que sube el cliente
        private Flux<DataBuffer> fileData;
    }

    /**
     * Para eliminar la imagen de un Vehiculo.
     * Dado que Imagen.id es Long, usamos Long en imagenId.
     */
    @Data
    public static class RemoveImagenRequest {
        private UUID vehiculoId;
        private Long imagenId;
    }

    @Data
    public static class VehiculoModeloRequest {
        private UUID marcaId;
        private VehiculoModelo vehiculoModelo;
    }

    @Data
    public static class VehiculoMotorRequest {
        private UUID marcaId;
        private VehiculoMotor vehiculoMotor;
    }

    @Data
    public static class VehiculoVersionRequest {
        private UUID marcaId;
        private VehiculoVersion vehiculoVersion;
    }
}
