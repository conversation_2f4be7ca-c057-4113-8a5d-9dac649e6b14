package corp.jamaro.jamaroservidor.security.service;

import corp.jamaro.jamaroservidor.security.model.UserAuth;
import corp.jamaro.jamaroservidor.security.repository.UserAuthRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserAuthService {

    private final UserAuthRepository userAuthRepository;

    public Mono<UserAuth> loadUserByUsername(String username) {
        log.debug("[UserAuthService] Cargando usuario por username: {}", username);
        return userAuthRepository.findByUsername(username)
                .doOnNext(u -> log.debug("[UserAuthService] Usuario {} encontrado: {}", username, u));
    }

    public Mono<UserAuth> loadUserByRfid(String rfid) {
        log.debug("[UserAuthService] Cargando usuario por RFID: {}", rfid);
        return userAuthRepository.findByRfid(rfid)
                .doOnNext(u -> log.debug("[UserAuthService] Usuario con RFID {} encontrado: {}", rfid, u));
    }

    public Mono<UserAuth> authenticate(String username, String password) {
        log.debug("[UserAuthService] Autenticando usuario {} con password sin encriptar", username);
        return loadUserByUsername(username)
                .flatMap(userAuth -> {
                    if (userAuth.getPassword().equals(password)) {
                        log.debug("[UserAuthService] Password correcto para el usuario {}", username);
                        return Mono.just(userAuth);
                    }
                    log.warn("[UserAuthService] Password incorrecto para el usuario {}", username);
                    return Mono.empty();
                });
    }

    public Mono<UserAuth> authenticateRfid(String rfid) {
        log.debug("[UserAuthService] Autenticando via RFID {}", rfid);
        return loadUserByRfid(rfid)
                .doOnNext(u -> log.debug("[UserAuthService] Autenticación via RFID exitosa para {}", u.getUsername()));
    }
}
