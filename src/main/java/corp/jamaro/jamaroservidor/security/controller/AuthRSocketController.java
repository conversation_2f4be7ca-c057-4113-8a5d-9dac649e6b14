package corp.jamaro.jamaroservidor.security.controller;

import corp.jamaro.jamaroservidor.security.dto.LoginRequest;
import corp.jamaro.jamaroservidor.security.dto.LoginResponse;
import corp.jamaro.jamaroservidor.security.dto.RfidLoginRequest;
import corp.jamaro.jamaroservidor.security.service.UserAuthService;
import corp.jamaro.jamaroservidor.security.util.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Mono;

@Controller
@RequiredArgsConstructor
@Slf4j
public class AuthRSocketController {

    private final UserAuthService userAuthService;
    private final JwtTokenProvider jwtTokenProvider;

    @MessageMapping("auth.login")
    public Mono<LoginResponse> login(Mono<LoginRequest> loginRequestMono) {
        return loginRequestMono.flatMap(req ->
                userAuthService.authenticate(req.getUsername(), req.getPassword())
                        .map(userAuth -> {
                            String token = jwtTokenProvider.createToken(
                                    userAuth,
                                    req.getClientType(),
                                    req.getClientVersion()
                            );
                            log.info("Usuario {} autenticado con clientType: {} y clientVersion: {}",
                                    req.getUsername(), req.getClientType(), req.getClientVersion());
                            return new LoginResponse(token, userAuth.getUser());
                        })
        );
    }

    @MessageMapping("auth.rfid")
    public Mono<LoginResponse> loginWithRfid(Mono<RfidLoginRequest> rfidRequestMono) {
        return rfidRequestMono.flatMap(req ->
                userAuthService.authenticateRfid(req.getRfid())
                        .map(userAuth -> {
                            String token = jwtTokenProvider.createToken(
                                    userAuth,
                                    req.getClientType(),
                                    req.getClientVersion()
                            );
                            log.info("Usuario {} autenticado vía RFID con clientType: {} y clientVersion: {}",
                                    userAuth.getUsername(), req.getClientType(), req.getClientVersion());
                            return new LoginResponse(token, userAuth.getUser());
                        })
        );
    }
}
