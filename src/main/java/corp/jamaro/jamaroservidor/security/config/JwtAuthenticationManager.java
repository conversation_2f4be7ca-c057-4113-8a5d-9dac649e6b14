package corp.jamaro.jamaroservidor.security.config;

import corp.jamaro.jamaroservidor.security.model.UserAuth;
import corp.jamaro.jamaroservidor.security.service.UserAuthService;
import corp.jamaro.jamaroservidor.security.util.JwtTokenProvider;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwtException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.ReactiveAuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import reactor.core.publisher.Mono;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationManager implements ReactiveAuthenticationManager {

    private final JwtTokenProvider jwtTokenProvider;
    private final UserAuthService userAuthService;

    @Override
    public Mono<Authentication> authenticate(Authentication authentication) throws AuthenticationException {
        String token = authentication.getCredentials().toString();
        log.debug("[JwtAuthenticationManager] Token recibido para autenticación: {}", token);

        // Remover prefijo "Bearer " si existe
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        log.debug("[JwtAuthenticationManager] Token sin prefijo Bearer: {}", token);

        try {
            Jws<Claims> claimsJws = jwtTokenProvider.validateToken(token);
            String username = claimsJws.getBody().getSubject();
            log.debug("[JwtAuthenticationManager] Token válido. Usuario extraído: {}", username);

            return userAuthService.loadUserByUsername(username)
                    .flatMap((UserAuth userAuth) -> {
                        if (!userAuth.isEnabled()) {
                            log.warn("[JwtAuthenticationManager] El usuario {} está deshabilitado", username);
                            return Mono.error(new BadCredentialsException("User disabled"));
                        }
                        log.debug("[JwtAuthenticationManager] Usuario {} habilitado con roles: {}", username, userAuth.getAuthorities());
                        Authentication authToken = new UsernamePasswordAuthenticationToken(
                                userAuth, null, userAuth.getAuthorities());
                        return Mono.just(authToken);
                    })
                    .switchIfEmpty(Mono.defer(() -> {
                        log.warn("[JwtAuthenticationManager] Usuario {} no encontrado en base de datos", username);
                        return Mono.error(new BadCredentialsException("User not found"));
                    }));
        } catch (JwtException e) {
            log.error("[JwtAuthenticationManager] Token inválido: {}", e.getMessage());
            return Mono.error(new BadCredentialsException("Invalid token", e));
        }
    }
}
