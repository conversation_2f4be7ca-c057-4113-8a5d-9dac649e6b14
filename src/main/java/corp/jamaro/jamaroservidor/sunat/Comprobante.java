package corp.jamaro.jamaroservidor.sunat;


import corp.jamaro.jamaroservidor.app.dinero.model.enums.TipoMoneda;
import corp.jamaro.jamaroservidor.app.model.Cliente;
import corp.jamaro.jamaroservidor.app.ventas.model.Sale;
import lombok.Data;
import org.springframework.data.neo4j.core.schema.GeneratedValue;
import org.springframework.data.neo4j.core.schema.Id;
import org.springframework.data.neo4j.core.schema.Node;
import org.springframework.data.neo4j.core.schema.Relationship;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Data
@Node
public class Comprobante {
    @Id
    @GeneratedValue(GeneratedValue.UUIDGenerator.class)
    private UUID id;

    private TipoComprobante tipoComprobante;//factura, boleta, nota de credito, nota de debito, etc.

    private String serie;
    private Long numero;

    private Instant fechaEmision;
    private Double montoTotal;
    private TipoMoneda moneda = TipoMoneda.SOLES;

    @Relationship(type = "DEL_CLIENTE")
    private List<Cliente> cliente;

    @Relationship(type = "DE_LA_VENTA")
    private Sale sale;

    //estos datos usualmente van a ser los mismos que del cliente pero pueden ser otros su el cliente así lo requiere ejemplo una factura a nombre de otra empresa etc.
    private String nombre;//el nombre completo que va en el comprobante
    private String dni;//el dni que va en el comprobante
    private String razonSocial;//la razon social que va en el comprobante
    private String direccion;//la direccion que va en el comprobante

    private String observaciones;

    private Boolean estado;

    public enum TipoComprobante {
        TICKET,
        PROFORMA,
        FACTURA,
        BOLETA,
        NOTA_CREDITO,
        NOTA_DEBITO
    }
}
