plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.0'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'org.graalvm.buildtools.native' version '0.9.28'
}

group = 'corp.jamaro'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Soporte Reactivo para Neo4j
    implementation 'org.springframework.boot:spring-boot-starter-data-neo4j'
    // Exposición mediante RSocket
    implementation 'org.springframework.boot:spring-boot-starter-rsocket'
    // Seguridad básica + Validación
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    // Seguridad para RSocket y OAuth2 Resource Server
    implementation 'org.springframework.security:spring-security-rsocket'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    // Para @ConfigurationProperties
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    // Desarrollo (DevTools)
    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // Test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

    // JWT con JJWT
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'

    // Minio
    implementation 'io.minio:minio:8.5.11'

    // Javers para auditorías
    implementation 'org.javers:javers-core:7.6.0'
    implementation 'org.javers:javers-persistence-sql:7.6.0'

    // Driver PostgreSQL
    runtimeOnly 'org.postgresql:postgresql'
}

tasks.named('test') {
    useJUnitPlatform()
}
